package com.example.castapp.manager.windowsettings;

/**
 * 窗口创建模块
 * 负责投屏窗口的创建和初始化逻辑
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000b\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u000e\n\u0002\u0010\u000b\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010\u0007\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\u0010\b\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u000b\n\u0002\u0018\u0002\n\u0002\b \u0018\u00002\u00020\u0001B\r\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J\u0018\u0010\r\u001a\u00020\t2\u0006\u0010\u000e\u001a\u00020\u000f2\u0006\u0010\u0010\u001a\u00020\u0011H\u0002J\u0018\u0010\u0012\u001a\u00020\t2\u0006\u0010\u000e\u001a\u00020\u000f2\u0006\u0010\u0010\u001a\u00020\u0011H\u0002J\u0018\u0010\u0013\u001a\u00020\t2\u0006\u0010\u000e\u001a\u00020\u000f2\u0006\u0010\u0010\u001a\u00020\u0011H\u0002J\u0014\u0010\u0014\u001a\u000e\u0012\u0004\u0012\u00020\u0016\u0012\u0004\u0012\u00020\u00160\u0015H\u0002J*\u0010\u0017\u001a\u000e\u0012\u0004\u0012\u00020\u0016\u0012\u0004\u0012\u00020\u00160\u00152\u0006\u0010\u0018\u001a\u00020\u00192\u0006\u0010\u001a\u001a\u00020\u00072\u0006\u0010\u001b\u001a\u00020\u001cJ\u001c\u0010\u001d\u001a\u000e\u0012\u0004\u0012\u00020\u0016\u0012\u0004\u0012\u00020\u00160\u00152\u0006\u0010\u001e\u001a\u00020\u0007H\u0002J\"\u0010\u001f\u001a\u000e\u0012\u0004\u0012\u00020\u0016\u0012\u0004\u0012\u00020\u00160\u00152\u0006\u0010 \u001a\u00020\u00162\u0006\u0010!\u001a\u00020\u0016J8\u0010\"\u001a\u00020\t2\u0006\u0010#\u001a\u00020\u00072\u0006\u0010$\u001a\u00020\u00072\u0006\u0010%\u001a\u00020\u00162\u0006\u0010&\u001a\u00020\u00162\u0006\u0010\u001b\u001a\u00020\u001c2\u0006\u0010\'\u001a\u00020(H\u0002JP\u0010)\u001a\u00020\t2\u0006\u0010*\u001a\u00020\u00072\u0006\u0010+\u001a\u00020\u00072\u0006\u0010,\u001a\u00020\u00072\u0006\u0010\u0018\u001a\u00020\u00192\u0006\u0010\u001a\u001a\u00020\u00072\u0006\u0010%\u001a\u00020\u00162\u0006\u0010&\u001a\u00020\u00162\u0006\u0010\u001b\u001a\u00020\u001c2\u0006\u0010\'\u001a\u00020(H\u0002J8\u0010-\u001a\u00020\t2\u0006\u0010\u001e\u001a\u00020\u00072\u0006\u0010.\u001a\u00020\u00072\u0006\u0010%\u001a\u00020\u00162\u0006\u0010&\u001a\u00020\u00162\u0006\u0010\u001b\u001a\u00020\u001c2\u0006\u0010\'\u001a\u00020(H\u0002J0\u0010/\u001a\u00020\t2\u0006\u00100\u001a\u00020\u00072\u0006\u0010%\u001a\u00020\u00162\u0006\u0010&\u001a\u00020\u00162\u0006\u0010\u001b\u001a\u00020\u001c2\u0006\u0010\'\u001a\u00020(H\u0002J\u0016\u00101\u001a\u00020\t2\u0006\u0010#\u001a\u00020\u00072\u0006\u0010$\u001a\u00020\u0007J8\u00102\u001a\u00020\t2\u0006\u0010#\u001a\u00020\u00072\u0006\u0010$\u001a\u00020\u00072\u0006\u0010%\u001a\u00020\u00162\u0006\u0010&\u001a\u00020\u00162\u0006\u0010\u001b\u001a\u00020\u001c2\u0006\u0010\'\u001a\u00020(H\u0002J@\u00103\u001a\u00020\t2\u0006\u0010#\u001a\u00020\u00072\u0006\u0010$\u001a\u00020\u00072\u0006\u0010%\u001a\u00020\u00162\u0006\u0010&\u001a\u00020\u00162\u0006\u0010\u001b\u001a\u00020\u001c2\u0006\u0010\'\u001a\u00020(2\u0006\u0010\u0010\u001a\u00020\u0011H\u0002J.\u00104\u001a\u00020\t2\u0006\u0010*\u001a\u00020\u00072\u0006\u0010+\u001a\u00020\u00072\u0006\u0010,\u001a\u00020\u00072\u0006\u0010\u0018\u001a\u00020\u00192\u0006\u0010\u001a\u001a\u00020\u0007JP\u00105\u001a\u00020\t2\u0006\u0010*\u001a\u00020\u00072\u0006\u0010+\u001a\u00020\u00072\u0006\u0010,\u001a\u00020\u00072\u0006\u0010\u0018\u001a\u00020\u00192\u0006\u0010\u001a\u001a\u00020\u00072\u0006\u0010%\u001a\u00020\u00162\u0006\u0010&\u001a\u00020\u00162\u0006\u0010\u001b\u001a\u00020\u001c2\u0006\u0010\'\u001a\u00020(H\u0002JV\u00106\u001a\u00020\t2\u0006\u0010*\u001a\u00020\u00072\u0006\u0010+\u001a\u00020\u00072\u0006\u0010,\u001a\u00020\u00072\u0006\u0010\u0018\u001a\u00020\u00192\u0006\u0010\u001a\u001a\u00020\u00072\u0006\u0010%\u001a\u00020\u00162\u0006\u0010&\u001a\u00020\u00162\u0006\u0010\u001b\u001a\u00020\u001c2\u0006\u0010\'\u001a\u00020(2\u0006\u0010\u0010\u001a\u00020\u0011J\u0016\u00107\u001a\u00020\t2\u0006\u0010\u001e\u001a\u00020\u00072\u0006\u0010.\u001a\u00020\u0007J8\u00108\u001a\u00020\t2\u0006\u0010\u001e\u001a\u00020\u00072\u0006\u0010.\u001a\u00020\u00072\u0006\u0010%\u001a\u00020\u00162\u0006\u0010&\u001a\u00020\u00162\u0006\u0010\u001b\u001a\u00020\u001c2\u0006\u0010\'\u001a\u00020(H\u0002J@\u00109\u001a\u00020\t2\u0006\u0010\u001e\u001a\u00020\u00072\u0006\u0010.\u001a\u00020\u00072\u0006\u0010%\u001a\u00020\u00162\u0006\u0010&\u001a\u00020\u00162\u0006\u0010\u001b\u001a\u00020\u001c2\u0006\u0010\'\u001a\u00020(2\u0006\u0010\u0010\u001a\u00020\u0011H\u0002J\u000e\u0010:\u001a\u00020\t2\u0006\u00100\u001a\u00020\u0007J0\u0010;\u001a\u00020\t2\u0006\u00100\u001a\u00020\u00072\u0006\u0010%\u001a\u00020\u00162\u0006\u0010&\u001a\u00020\u00162\u0006\u0010\u001b\u001a\u00020\u001c2\u0006\u0010\'\u001a\u00020(H\u0002J8\u0010<\u001a\u00020\t2\u0006\u00100\u001a\u00020\u00072\u0006\u0010%\u001a\u00020\u00162\u0006\u0010&\u001a\u00020\u00162\u0006\u0010\u001b\u001a\u00020\u001c2\u0006\u0010\'\u001a\u00020(2\u0006\u0010\u0010\u001a\u00020\u0011H\u0002J$\u0010=\u001a\u000e\u0012\u0004\u0012\u00020\u0016\u0012\u0004\u0012\u00020\u00160\u00152\u0006\u0010\u0018\u001a\u00020\u00192\u0006\u0010\u001b\u001a\u00020\u001cH\u0002J$\u0010>\u001a\u000e\u0012\u0004\u0012\u00020\u0016\u0012\u0004\u0012\u00020\u00160\u00152\u0006\u0010\u0018\u001a\u00020\u00192\u0006\u0010\u001b\u001a\u00020\u001cH\u0002J\u0010\u0010?\u001a\u00020\b2\u0006\u00100\u001a\u00020\u0007H\u0002J\u0018\u0010@\u001a\u00020\t2\u0006\u00100\u001a\u00020\u00072\u0006\u0010A\u001a\u00020\bH\u0002J\u0018\u0010B\u001a\u00020\t2\u0006\u0010\u000e\u001a\u00020\u000f2\u0006\u0010\u0010\u001a\u00020\u0011H\u0002J \u0010C\u001a\u00020\t2\u0018\u0010D\u001a\u0014\u0012\u0004\u0012\u00020\u0007\u0012\u0004\u0012\u00020\b\u0012\u0004\u0012\u00020\t0\u0006J2\u0010E\u001a\u00020\t2*\u0010D\u001a&\u0012\u0004\u0012\u00020\u0007\u0012\u0004\u0012\u00020\f\u0012\u0004\u0012\u00020\f\u0012\u0004\u0012\u00020\f\u0012\u0004\u0012\u00020\f\u0012\u0004\u0012\u00020\t0\u000bJ\u0010\u0010F\u001a\u00020\t2\u0006\u0010\u000e\u001a\u00020\u000fH\u0002J\u0018\u0010G\u001a\u00020\t2\u0006\u0010\u0010\u001a\u00020\u00112\u0006\u0010\u001b\u001a\u00020\u001cH\u0002R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\"\u0010\u0005\u001a\u0016\u0012\u0004\u0012\u00020\u0007\u0012\u0004\u0012\u00020\b\u0012\u0004\u0012\u00020\t\u0018\u00010\u0006X\u0082\u000e\u00a2\u0006\u0002\n\u0000R4\u0010\n\u001a(\u0012\u0004\u0012\u00020\u0007\u0012\u0004\u0012\u00020\f\u0012\u0004\u0012\u00020\f\u0012\u0004\u0012\u00020\f\u0012\u0004\u0012\u00020\f\u0012\u0004\u0012\u00020\t\u0018\u00010\u000bX\u0082\u000e\u00a2\u0006\u0002\n\u0000\u00a8\u0006H"}, d2 = {"Lcom/example/castapp/manager/windowsettings/WindowCreationModule;", "", "dataModule", "Lcom/example/castapp/manager/windowsettings/WindowDataModule;", "(Lcom/example/castapp/manager/windowsettings/WindowDataModule;)V", "onCropModeChangeCallback", "Lkotlin/Function2;", "", "", "", "transformValueChangeCallback", "Lkotlin/Function5;", "", "applyCompleteLayoutParameters", "transformHandler", "Lcom/example/castapp/ui/windowsettings/TransformHandler;", "layoutItem", "Lcom/example/castapp/database/entity/WindowLayoutItemEntity;", "applyCropParametersDelayed", "applyLayoutParametersExceptCrop", "calculateCameraWindowSize", "Lkotlin/Pair;", "", "calculateMediaWindowSize", "uri", "Landroid/net/Uri;", "contentType", "activity", "Landroid/app/Activity;", "calculateTextWindowSize", "textId", "calculateWindowSize", "originalWidth", "originalHeight", "checkAndCreateCameraWindowWithLayout", "cameraId", "cameraName", "windowWidth", "windowHeight", "container", "Landroid/widget/FrameLayout;", "checkAndCreateMediaWindowWithLayout", "mediaId", "mediaType", "fileName", "checkAndCreateTextWindowWithLayout", "textContent", "checkAndCreateWindowWithLayout", "connectionId", "createCameraWindow", "createCameraWindowWithDefaultParameters", "createCameraWindowWithLayoutParameters", "createMediaWindow", "createMediaWindowWithDefaultParameters", "createMediaWindowWithLayoutParameters", "createTextWindow", "createTextWindowWithDefaultParameters", "createTextWindowWithLayoutParameters", "createWindowForConnection", "createWindowWithDefaultParameters", "createWindowWithLayoutParameters", "getImageResolution", "getVideoResolution", "isRealCastWindow", "restoreLandscapeModeStateForNewWindow", "isEnabled", "restoreVideoPlaybackStateForCreation", "setOnCropModeChangeCallback", "callback", "setTransformValueChangeCallback", "setupTransformHandlerCallbacks", "syncNoteToSharedPreferences", "app_debug"})
public final class WindowCreationModule {
    @org.jetbrains.annotations.NotNull()
    private final com.example.castapp.manager.windowsettings.WindowDataModule dataModule = null;
    @org.jetbrains.annotations.Nullable()
    private kotlin.jvm.functions.Function5<? super java.lang.String, ? super java.lang.Float, ? super java.lang.Float, ? super java.lang.Float, ? super java.lang.Float, kotlin.Unit> transformValueChangeCallback;
    @org.jetbrains.annotations.Nullable()
    private kotlin.jvm.functions.Function2<? super java.lang.String, ? super java.lang.Boolean, kotlin.Unit> onCropModeChangeCallback;
    
    public WindowCreationModule(@org.jetbrains.annotations.NotNull()
    com.example.castapp.manager.windowsettings.WindowDataModule dataModule) {
        super();
    }
    
    /**
     * 设置变换值变化回调
     */
    public final void setTransformValueChangeCallback(@org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function5<? super java.lang.String, ? super java.lang.Float, ? super java.lang.Float, ? super java.lang.Float, ? super java.lang.Float, kotlin.Unit> callback) {
    }
    
    /**
     * 🎯 新增：设置裁剪模式变化回调
     */
    public final void setOnCropModeChangeCallback(@org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function2<? super java.lang.String, ? super java.lang.Boolean, kotlin.Unit> callback) {
    }
    
    /**
     * 计算窗口尺寸（基于发送端分辨率）
     */
    @org.jetbrains.annotations.NotNull()
    public final kotlin.Pair<java.lang.Integer, java.lang.Integer> calculateWindowSize(int originalWidth, int originalHeight) {
        return null;
    }
    
    /**
     * 为连接创建投屏窗口
     */
    public final void createWindowForConnection(@org.jetbrains.annotations.NotNull()
    java.lang.String connectionId) {
    }
    
    /**
     * 🏷️ 同步单个布局项的备注信息到SharedPreferences
     */
    private final void syncNoteToSharedPreferences(com.example.castapp.database.entity.WindowLayoutItemEntity layoutItem, android.app.Activity activity) {
    }
    
    /**
     * 检查并使用布局参数创建窗口
     */
    private final void checkAndCreateWindowWithLayout(java.lang.String connectionId, int windowWidth, int windowHeight, android.app.Activity activity, android.widget.FrameLayout container) {
    }
    
    /**
     * 使用布局参数创建窗口
     */
    private final void createWindowWithLayoutParameters(java.lang.String connectionId, int windowWidth, int windowHeight, android.app.Activity activity, android.widget.FrameLayout container, com.example.castapp.database.entity.WindowLayoutItemEntity layoutItem) {
    }
    
    /**
     * 使用默认参数创建窗口
     */
    private final void createWindowWithDefaultParameters(java.lang.String connectionId, int windowWidth, int windowHeight, android.app.Activity activity, android.widget.FrameLayout container) {
    }
    
    /**
     * 设置TransformHandler回调
     */
    private final void setupTransformHandlerCallbacks(com.example.castapp.ui.windowsettings.TransformHandler transformHandler) {
    }
    
    /**
     * 应用布局参数（除裁剪外）
     */
    private final void applyLayoutParametersExceptCrop(com.example.castapp.ui.windowsettings.TransformHandler transformHandler, com.example.castapp.database.entity.WindowLayoutItemEntity layoutItem) {
    }
    
    /**
     * 🎯 判断是否为真实的投屏窗口（非本地窗口）
     */
    private final boolean isRealCastWindow(java.lang.String connectionId) {
        return false;
    }
    
    /**
     * 🎯 为新创建的窗口恢复横屏模式状态
     * 用于首次投屏时的自动布局应用
     */
    private final void restoreLandscapeModeStateForNewWindow(java.lang.String connectionId, boolean isEnabled) {
    }
    
    /**
     * 延迟应用裁剪参数
     */
    private final void applyCropParametersDelayed(com.example.castapp.ui.windowsettings.TransformHandler transformHandler, com.example.castapp.database.entity.WindowLayoutItemEntity layoutItem) {
    }
    
    /**
     * 创建摄像头窗口
     */
    public final void createCameraWindow(@org.jetbrains.annotations.NotNull()
    java.lang.String cameraId, @org.jetbrains.annotations.NotNull()
    java.lang.String cameraName) {
    }
    
    /**
     * 创建媒体窗口（视频/图片）
     */
    public final void createMediaWindow(@org.jetbrains.annotations.NotNull()
    java.lang.String mediaId, @org.jetbrains.annotations.NotNull()
    java.lang.String mediaType, @org.jetbrains.annotations.NotNull()
    java.lang.String fileName, @org.jetbrains.annotations.NotNull()
    android.net.Uri uri, @org.jetbrains.annotations.NotNull()
    java.lang.String contentType) {
    }
    
    /**
     * 创建文本窗口
     */
    public final void createTextWindow(@org.jetbrains.annotations.NotNull()
    java.lang.String textId, @org.jetbrains.annotations.NotNull()
    java.lang.String textContent) {
    }
    
    /**
     * 检查并使用布局参数创建摄像头窗口
     */
    private final void checkAndCreateCameraWindowWithLayout(java.lang.String cameraId, java.lang.String cameraName, int windowWidth, int windowHeight, android.app.Activity activity, android.widget.FrameLayout container) {
    }
    
    /**
     * 计算摄像头窗口尺寸
     */
    private final kotlin.Pair<java.lang.Integer, java.lang.Integer> calculateCameraWindowSize() {
        return null;
    }
    
    /**
     * 使用布局参数创建摄像头窗口
     */
    private final void createCameraWindowWithLayoutParameters(java.lang.String cameraId, java.lang.String cameraName, int windowWidth, int windowHeight, android.app.Activity activity, android.widget.FrameLayout container, com.example.castapp.database.entity.WindowLayoutItemEntity layoutItem) {
    }
    
    /**
     * 使用默认参数创建摄像头窗口
     */
    private final void createCameraWindowWithDefaultParameters(java.lang.String cameraId, java.lang.String cameraName, int windowWidth, int windowHeight, android.app.Activity activity, android.widget.FrameLayout container) {
    }
    
    /**
     * 计算媒体窗口尺寸（视频/图片分辨率 × 0.4）（公开方法，供WindowLayoutModule调用）
     */
    @org.jetbrains.annotations.NotNull()
    public final kotlin.Pair<java.lang.Integer, java.lang.Integer> calculateMediaWindowSize(@org.jetbrains.annotations.NotNull()
    android.net.Uri uri, @org.jetbrains.annotations.NotNull()
    java.lang.String contentType, @org.jetbrains.annotations.NotNull()
    android.app.Activity activity) {
        return null;
    }
    
    /**
     * 计算文本窗口尺寸
     */
    private final kotlin.Pair<java.lang.Integer, java.lang.Integer> calculateTextWindowSize(java.lang.String textId) {
        return null;
    }
    
    /**
     * 获取视频分辨率（考虑旋转信息）
     */
    private final kotlin.Pair<java.lang.Integer, java.lang.Integer> getVideoResolution(android.net.Uri uri, android.app.Activity activity) {
        return null;
    }
    
    /**
     * 获取图片分辨率
     */
    private final kotlin.Pair<java.lang.Integer, java.lang.Integer> getImageResolution(android.net.Uri uri, android.app.Activity activity) {
        return null;
    }
    
    /**
     * 检查并使用布局参数创建媒体窗口
     */
    private final void checkAndCreateMediaWindowWithLayout(java.lang.String mediaId, java.lang.String mediaType, java.lang.String fileName, android.net.Uri uri, java.lang.String contentType, int windowWidth, int windowHeight, android.app.Activity activity, android.widget.FrameLayout container) {
    }
    
    /**
     * 使用布局参数创建媒体窗口（公开方法，供WindowLayoutModule调用）
     */
    public final void createMediaWindowWithLayoutParameters(@org.jetbrains.annotations.NotNull()
    java.lang.String mediaId, @org.jetbrains.annotations.NotNull()
    java.lang.String mediaType, @org.jetbrains.annotations.NotNull()
    java.lang.String fileName, @org.jetbrains.annotations.NotNull()
    android.net.Uri uri, @org.jetbrains.annotations.NotNull()
    java.lang.String contentType, int windowWidth, int windowHeight, @org.jetbrains.annotations.NotNull()
    android.app.Activity activity, @org.jetbrains.annotations.NotNull()
    android.widget.FrameLayout container, @org.jetbrains.annotations.NotNull()
    com.example.castapp.database.entity.WindowLayoutItemEntity layoutItem) {
    }
    
    /**
     * 使用默认参数创建媒体窗口
     */
    private final void createMediaWindowWithDefaultParameters(java.lang.String mediaId, java.lang.String mediaType, java.lang.String fileName, android.net.Uri uri, java.lang.String contentType, int windowWidth, int windowHeight, android.app.Activity activity, android.widget.FrameLayout container) {
    }
    
    /**
     * 检查并使用布局参数创建文本窗口
     */
    private final void checkAndCreateTextWindowWithLayout(java.lang.String textId, java.lang.String textContent, int windowWidth, int windowHeight, android.app.Activity activity, android.widget.FrameLayout container) {
    }
    
    /**
     * 使用布局参数创建文本窗口
     */
    private final void createTextWindowWithLayoutParameters(java.lang.String textId, java.lang.String textContent, int windowWidth, int windowHeight, android.app.Activity activity, android.widget.FrameLayout container, com.example.castapp.database.entity.WindowLayoutItemEntity layoutItem) {
    }
    
    /**
     * 使用默认参数创建文本窗口
     */
    private final void createTextWindowWithDefaultParameters(java.lang.String textId, java.lang.String textContent, int windowWidth, int windowHeight, android.app.Activity activity, android.widget.FrameLayout container) {
    }
    
    /**
     * 📁 应用完整的布局参数（复用WindowLayoutModule的精确逻辑）
     */
    private final void applyCompleteLayoutParameters(com.example.castapp.ui.windowsettings.TransformHandler transformHandler, com.example.castapp.database.entity.WindowLayoutItemEntity layoutItem) {
    }
    
    /**
     * 🎬 恢复视频播放状态（用于媒体窗口创建）
     */
    private final void restoreVideoPlaybackStateForCreation(com.example.castapp.ui.windowsettings.TransformHandler transformHandler, com.example.castapp.database.entity.WindowLayoutItemEntity layoutItem) {
    }
}