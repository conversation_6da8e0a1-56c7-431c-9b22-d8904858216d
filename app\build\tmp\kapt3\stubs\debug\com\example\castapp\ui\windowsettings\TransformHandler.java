package com.example.castapp.ui.windowsettings;

/**
 * 重构后的变换处理器（协调器模式）
 * 负责协调各个管理器组件，提供统一的对外接口
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u00bc\u0001\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\b\n\u0002\b\u0004\n\u0002\u0010\u0007\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0002\b\u000b\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\u0007\n\u0002\u0018\u0002\n\u0002\b\f\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u001d\n\u0002\u0018\u0002\n\u0002\b!\n\u0002\u0018\u0002\n\u0002\b\u0010\u0018\u0000 \u00a0\u00012\u00020\u0001:\n\u00a0\u0001\u00a1\u0001\u00a2\u0001\u00a3\u0001\u00a4\u0001B9\b\u0007\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\n\b\u0002\u0010\u0004\u001a\u0004\u0018\u00010\u0005\u0012\b\b\u0002\u0010\u0006\u001a\u00020\u0007\u0012\b\b\u0002\u0010\b\u001a\u00020\u0007\u0012\b\b\u0002\u0010\t\u001a\u00020\u0007\u00a2\u0006\u0002\u0010\nJ\b\u0010:\u001a\u00020!H\u0002J\b\u0010;\u001a\u00020!H\u0016J\u0012\u0010<\u001a\u0004\u0018\u00010=2\b\b\u0002\u0010>\u001a\u00020\u0012J\u0006\u0010?\u001a\u00020!J\u0018\u0010@\u001a\u00020#2\u0006\u0010A\u001a\u00020\f2\u0006\u0010B\u001a\u00020\fH\u0002J\u0010\u0010C\u001a\u00020\u00122\u0006\u0010D\u001a\u00020EH\u0016J\u0010\u0010F\u001a\u00020!2\b\b\u0002\u0010G\u001a\u00020\u0012J\u0006\u0010H\u001a\u00020\fJ\u0006\u0010I\u001a\u00020\fJ\u0006\u0010J\u001a\u00020\u0007J\u0006\u0010K\u001a\u00020\u0007J\u0006\u0010L\u001a\u00020\u0007J\u0006\u0010M\u001a\u00020\fJ\u0006\u0010N\u001a\u00020\u000eJ\u0006\u0010O\u001a\u00020\fJ\u0006\u0010P\u001a\u00020\fJ\b\u0010Q\u001a\u0004\u0018\u00010RJ\u0006\u0010S\u001a\u00020\fJ\b\u0010T\u001a\u0004\u0018\u00010UJ\u0006\u0010V\u001a\u00020\fJ\u0006\u0010W\u001a\u00020\fJ\b\u0010X\u001a\u0004\u0018\u00010\u001eJ\u0010\u0010Y\u001a\u00020\u000e2\u0006\u0010Z\u001a\u00020\u0007H\u0002J\b\u0010[\u001a\u0004\u0018\u000101J\u0006\u0010\\\u001a\u00020\fJ\u0006\u0010]\u001a\u00020\u0012J\u0006\u0010^\u001a\u00020\u0012J\u0006\u0010\u0013\u001a\u00020\u0012J\u0006\u0010_\u001a\u00020\u0012J\u0006\u0010\u0018\u001a\u00020\u0012J\u0006\u0010\u0019\u001a\u00020\u0012J\u0018\u0010`\u001a\u00020\u00122\u0006\u0010A\u001a\u00020\f2\u0006\u0010B\u001a\u00020\fH\u0002J\u0006\u0010a\u001a\u00020!J\u0010\u0010b\u001a\u00020\u00122\u0006\u0010D\u001a\u00020EH\u0016J\b\u0010c\u001a\u00020\u0012H\u0016J\u0016\u0010d\u001a\u00020!2\u0006\u0010e\u001a\u00020\f2\u0006\u0010f\u001a\u00020\fJ\u0018\u0010g\u001a\u00020!2\u0006\u0010e\u001a\u00020\f2\u0006\u0010f\u001a\u00020\fH\u0002J\u0006\u0010h\u001a\u00020!J\u000e\u0010i\u001a\u00020!2\u0006\u0010j\u001a\u00020\u0007J\u000e\u0010k\u001a\u00020!2\u0006\u0010l\u001a\u00020\u0012J\u000e\u0010m\u001a\u00020!2\u0006\u0010n\u001a\u00020\fJ\u000e\u0010o\u001a\u00020!2\u0006\u0010p\u001a\u00020\fJ\u001a\u0010q\u001a\u00020!2\u0012\u0010r\u001a\u000e\u0012\u0004\u0012\u00020\u0012\u0012\u0004\u0012\u00020!0sJ\u0010\u0010t\u001a\u00020!2\b\u0010u\u001a\u0004\u0018\u00010UJ \u0010v\u001a\u00020!2\b\u0010w\u001a\u0004\u0018\u00010\u000e2\u0006\u0010x\u001a\u00020\u000e2\u0006\u0010y\u001a\u00020\u0007J\u000e\u0010z\u001a\u00020!2\u0006\u0010l\u001a\u00020\u0012J\u000e\u0010{\u001a\u00020!2\u0006\u0010l\u001a\u00020\u0012J \u0010|\u001a\u00020!2\u0018\u0010}\u001a\u0014\u0012\u0004\u0012\u00020\u000e\u0012\u0004\u0012\u00020\u0012\u0012\u0004\u0012\u00020!0 J\'\u0010~\u001a\u00020!2\u0006\u0010A\u001a\u00020\f2\u0006\u0010B\u001a\u00020\f2\u0006\u0010\u007f\u001a\u00020\f2\u0007\u0010\u0080\u0001\u001a\u00020\fJ\u000f\u0010\u0081\u0001\u001a\u00020!2\u0006\u0010l\u001a\u00020\u0012J\u000f\u0010\u0082\u0001\u001a\u00020!2\u0006\u0010l\u001a\u00020\u0012J#\u0010\u0083\u0001\u001a\u00020!2\u001a\u0010r\u001a\u0016\u0012\u0006\u0012\u0004\u0018\u00010+\u0012\u0004\u0012\u00020\u000e\u0012\u0004\u0012\u00020!0 J3\u0010\u0084\u0001\u001a\u00020!2*\u0010r\u001a&\u0012\u0004\u0012\u00020\u000e\u0012\u0004\u0012\u00020\f\u0012\u0004\u0012\u00020\f\u0012\u0004\u0012\u00020\f\u0012\u0004\u0012\u00020\f\u0012\u0004\u0012\u00020!03J%\u0010\u0085\u0001\u001a\u00020!2\u0006\u0010Z\u001a\u00020\u00072\t\b\u0002\u0010\u0086\u0001\u001a\u00020\u00072\t\b\u0002\u0010\u0087\u0001\u001a\u00020\u0007J\u0012\u0010\u0088\u0001\u001a\u00020!2\u0007\u0010\u0089\u0001\u001a\u00020\u0007H\u0016J\u0010\u0010\u008a\u0001\u001a\u00020!2\u0007\u0010\u008b\u0001\u001a\u00020\fJ\u0011\u0010\u008c\u0001\u001a\u00020!2\u0006\u0010\u000f\u001a\u00020\u0010H\u0002J\u0019\u0010\u008d\u0001\u001a\u00020!2\u0007\u0010\u008e\u0001\u001a\u00020\u000e2\u0007\u0010\u008f\u0001\u001a\u00020\u000eJ\u000f\u0010\u0090\u0001\u001a\u00020!2\u0006\u0010\r\u001a\u00020\u000eJ,\u0010\u0091\u0001\u001a\u00020!2\u0007\u0010\u0092\u0001\u001a\u00020\u000e2\u0007\u0010\u0093\u0001\u001a\u00020\u000e2\b\u0010\u0094\u0001\u001a\u00030\u0095\u00012\u0007\u0010\u0096\u0001\u001a\u00020\u000eJ\u0019\u0010\u0097\u0001\u001a\u00020!2\u0007\u0010\u0098\u0001\u001a\u00020\u000e2\u0007\u0010\u0099\u0001\u001a\u00020\u000eJ\t\u0010\u009a\u0001\u001a\u00020!H\u0002J\u0007\u0010\u009b\u0001\u001a\u00020!J\u0019\u0010\u009c\u0001\u001a\u00020!2\u0007\u0010\u009d\u0001\u001a\u00020\u00072\u0007\u0010\u009e\u0001\u001a\u00020\u0007J\u0007\u0010\u009f\u0001\u001a\u00020!R\u000e\u0010\u000b\u001a\u00020\fX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\r\u001a\u00020\u000eX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000f\u001a\u00020\u0010X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0011\u001a\u00020\u0012X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0013\u001a\u00020\u0012X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0014\u001a\u00020\u0012X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0015\u001a\u00020\u0012X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0016\u001a\u00020\u0012X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0017\u001a\u00020\u0012X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0018\u001a\u00020\u0012X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0019\u001a\u00020\u0012X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u001a\u001a\u00020\u0012X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u001b\u001a\u00020\fX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u001c\u001a\u00020\fX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0010\u0010\u001d\u001a\u0004\u0018\u00010\u001eX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\"\u0010\u001f\u001a\u0016\u0012\u0004\u0012\u00020\u000e\u0012\u0004\u0012\u00020\u0012\u0012\u0004\u0012\u00020!\u0018\u00010 X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\"\u001a\u00020#X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010$\u001a\u00020%X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010&\u001a\u00020\'X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010(\u001a\u00020)X\u0082\u0004\u00a2\u0006\u0002\n\u0000R$\u0010*\u001a\u0018\u0012\u0006\u0012\u0004\u0018\u00010+\u0012\u0004\u0012\u00020\u000e\u0012\u0004\u0012\u00020!\u0018\u00010 X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010,\u001a\u00020-X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0010\u0010.\u001a\u0004\u0018\u00010/X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0010\u00100\u001a\u0004\u0018\u000101X\u0082\u000e\u00a2\u0006\u0002\n\u0000R4\u00102\u001a(\u0012\u0004\u0012\u00020\u000e\u0012\u0004\u0012\u00020\f\u0012\u0004\u0012\u00020\f\u0012\u0004\u0012\u00020\f\u0012\u0004\u0012\u00020\f\u0012\u0004\u0012\u00020!\u0018\u000103X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u00104\u001a\u000205X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u00106\u001a\u000207X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u00108\u001a\u00020\u0007X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u00109\u001a\u00020\u0007X\u0082\u000e\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u00a5\u0001"}, d2 = {"Lcom/example/castapp/ui/windowsettings/TransformHandler;", "Landroid/widget/FrameLayout;", "context", "Landroid/content/Context;", "attrs", "Landroid/util/AttributeSet;", "defStyleAttr", "", "initialWidth", "initialHeight", "(Landroid/content/Context;Landroid/util/AttributeSet;III)V", "borderTouchThreshold", "", "connectionId", "", "cropManager", "Lcom/example/castapp/ui/windowsettings/CropManager;", "isBorderResizeEnabled", "", "isDragEnabled", "isDragging", "isMirrored", "isResizing", "isRotating", "isRotationEnabled", "isScaleEnabled", "isScaling", "lastTouchX", "lastTouchY", "mediaSurfaceManager", "Lcom/example/castapp/ui/windowsettings/MediaSurfaceManager;", "onCropModeChangeListener", "Lkotlin/Function2;", "", "resizeMode", "Lcom/example/castapp/ui/windowsettings/TransformHandler$ResizeMode;", "rotationGestureDetector", "Lcom/example/castapp/ui/windowsettings/TransformHandler$RotationGestureDetector;", "scaleGestureDetector", "Landroid/view/ScaleGestureDetector;", "screenshotManager", "Lcom/example/castapp/ui/windowsettings/ScreenshotManager;", "surfaceAvailableCallback", "Landroid/view/Surface;", "surfaceManager", "Lcom/example/castapp/ui/windowsettings/SurfaceManager;", "textDoubleClickDetector", "Landroid/view/GestureDetector;", "textWindowManager", "Lcom/example/castapp/ui/windowsettings/TextWindowManager;", "transformChangeCallback", "Lkotlin/Function5;", "transformManager", "Lcom/example/castapp/ui/windowsettings/TransformManager;", "transformRenderer", "Lcom/example/castapp/ui/windowsettings/TransformRenderer;", "windowHeight", "windowWidth", "applyTransforms", "bringToFront", "captureScreenshot", "Landroid/graphics/Bitmap;", "forRemoteControl", "cleanup", "detectResizeMode", "x", "y", "dispatchTouchEvent", "event", "Landroid/view/MotionEvent;", "endCropMode", "isCancel", "getActualDisplayX", "getActualDisplayY", "getBaseWindowHeight", "getBaseWindowWidth", "getBorderColor", "getBorderWidth", "getConnectionId", "getContainerDisplayX", "getContainerDisplayY", "getContainerView", "Landroid/view/View;", "getCornerRadius", "getCropRectRatio", "Landroid/graphics/RectF;", "getCurrentRotation", "getCurrentScaleFactor", "getMediaSurfaceManager", "getOrientationName", "orientation", "getTextWindowManager", "getWindowAlpha", "isBorderEnabled", "isCroppingMode", "isMirrorEnabled", "isTouchInVisibleArea", "notifyDragEnd", "onTouchEvent", "performClick", "performDragByHandle", "deltaX", "deltaY", "performResize", "resetTransform", "setBorderColor", "color", "setBorderEnabled", "enabled", "setBorderWidth", "width", "setCornerRadius", "radius", "setCropModeChangeCallback", "callback", "Lkotlin/Function1;", "setCropRectRatio", "rectRatio", "setDeviceInfo", "deviceName", "ipAddress", "port", "setDragEnabled", "setMirrorEnabled", "setOnCropModeChangeListener", "listener", "setPrecisionTransform", "scale", "rotation", "setRotationEnabled", "setScaleEnabled", "setSurfaceAvailableCallback", "setTransformChangeCallback", "setVideoOrientation", "videoWidth", "videoHeight", "setVisibility", "visibility", "setWindowAlpha", "alphaValue", "setupCropManagerListener", "setupForCameraConnection", "cameraId", "cameraName", "setupForConnection", "setupForMediaConnection", "mediaId", "fileName", "uri", "Landroid/net/Uri;", "contentType", "setupForTextConnection", "textId", "textContent", "setupManagerListeners", "startCropMode", "updateBaseWindowSize", "newWidth", "newHeight", "updateUnifiedBorderAfterResize", "Companion", "ResizeMode", "RotationGestureDetector", "RotationGestureListener", "ScaleGestureListener", "app_debug"})
public final class TransformHandler extends android.widget.FrameLayout {
    public static final int DEFAULT_WINDOW_WIDTH = 400;
    public static final int DEFAULT_WINDOW_HEIGHT = 600;
    private int windowWidth;
    private int windowHeight;
    @org.jetbrains.annotations.NotNull()
    private java.lang.String connectionId = "";
    @org.jetbrains.annotations.NotNull()
    private final com.example.castapp.ui.windowsettings.SurfaceManager surfaceManager = null;
    @org.jetbrains.annotations.NotNull()
    private final com.example.castapp.ui.windowsettings.ScreenshotManager screenshotManager = null;
    @org.jetbrains.annotations.NotNull()
    private final com.example.castapp.ui.windowsettings.TransformRenderer transformRenderer = null;
    @org.jetbrains.annotations.NotNull()
    private final com.example.castapp.ui.windowsettings.TransformManager transformManager = null;
    @org.jetbrains.annotations.NotNull()
    private com.example.castapp.ui.windowsettings.CropManager cropManager;
    @org.jetbrains.annotations.Nullable()
    private com.example.castapp.ui.windowsettings.MediaSurfaceManager mediaSurfaceManager;
    @org.jetbrains.annotations.Nullable()
    private com.example.castapp.ui.windowsettings.TextWindowManager textWindowManager;
    @org.jetbrains.annotations.NotNull()
    private final android.view.ScaleGestureDetector scaleGestureDetector = null;
    @org.jetbrains.annotations.NotNull()
    private final com.example.castapp.ui.windowsettings.TransformHandler.RotationGestureDetector rotationGestureDetector = null;
    @org.jetbrains.annotations.Nullable()
    private android.view.GestureDetector textDoubleClickDetector;
    private boolean isDragging = false;
    private boolean isScaling = false;
    private boolean isRotating = false;
    private float lastTouchX = 0.0F;
    private float lastTouchY = 0.0F;
    private boolean isResizing = false;
    @org.jetbrains.annotations.NotNull()
    private com.example.castapp.ui.windowsettings.TransformHandler.ResizeMode resizeMode = com.example.castapp.ui.windowsettings.TransformHandler.ResizeMode.NONE;
    private float borderTouchThreshold = 20.0F;
    private boolean isDragEnabled = false;
    private boolean isScaleEnabled = false;
    private boolean isRotationEnabled = false;
    private boolean isMirrored = false;
    private boolean isBorderResizeEnabled = false;
    @org.jetbrains.annotations.Nullable()
    private kotlin.jvm.functions.Function2<? super android.view.Surface, ? super java.lang.String, kotlin.Unit> surfaceAvailableCallback;
    @org.jetbrains.annotations.Nullable()
    private kotlin.jvm.functions.Function5<? super java.lang.String, ? super java.lang.Float, ? super java.lang.Float, ? super java.lang.Float, ? super java.lang.Float, kotlin.Unit> transformChangeCallback;
    @org.jetbrains.annotations.Nullable()
    private kotlin.jvm.functions.Function2<? super java.lang.String, ? super java.lang.Boolean, kotlin.Unit> onCropModeChangeListener;
    @org.jetbrains.annotations.NotNull()
    public static final com.example.castapp.ui.windowsettings.TransformHandler.Companion Companion = null;
    
    @kotlin.jvm.JvmOverloads()
    public TransformHandler(@org.jetbrains.annotations.NotNull()
    android.content.Context context, @org.jetbrains.annotations.Nullable()
    android.util.AttributeSet attrs, int defStyleAttr, int initialWidth, int initialHeight) {
        super(null);
    }
    
    /**
     * 设置管理器间的监听器
     */
    private final void setupManagerListeners() {
    }
    
    /**
     * 为指定连接设置TextureView和Surface
     */
    public final void setupForConnection(@org.jetbrains.annotations.NotNull()
    java.lang.String connectionId) {
    }
    
    /**
     * 为摄像头连接设置TextureView和Surface
     */
    public final void setupForCameraConnection(@org.jetbrains.annotations.NotNull()
    java.lang.String cameraId, @org.jetbrains.annotations.NotNull()
    java.lang.String cameraName) {
    }
    
    /**
     * 为媒体连接设置VideoView或ImageView
     */
    public final void setupForMediaConnection(@org.jetbrains.annotations.NotNull()
    java.lang.String mediaId, @org.jetbrains.annotations.NotNull()
    java.lang.String fileName, @org.jetbrains.annotations.NotNull()
    android.net.Uri uri, @org.jetbrains.annotations.NotNull()
    java.lang.String contentType) {
    }
    
    /**
     * 为文本连接设置TextView
     */
    public final void setupForTextConnection(@org.jetbrains.annotations.NotNull()
    java.lang.String textId, @org.jetbrains.annotations.NotNull()
    java.lang.String textContent) {
    }
    
    /**
     * 设置CropManager监听器
     */
    private final void setupCropManagerListener(com.example.castapp.ui.windowsettings.CropManager cropManager) {
    }
    
    /**
     * 设置设备信息
     */
    public final void setDeviceInfo(@org.jetbrains.annotations.Nullable()
    java.lang.String deviceName, @org.jetbrains.annotations.NotNull()
    java.lang.String ipAddress, int port) {
    }
    
    /**
     * 应用所有变换
     */
    private final void applyTransforms() {
    }
    
    @java.lang.Override()
    public boolean dispatchTouchEvent(@org.jetbrains.annotations.NotNull()
    android.view.MotionEvent event) {
        return false;
    }
    
    @java.lang.Override()
    public boolean onTouchEvent(@org.jetbrains.annotations.NotNull()
    android.view.MotionEvent event) {
        return false;
    }
    
    @java.lang.Override()
    public boolean performClick() {
        return false;
    }
    
    /**
     * 设置Surface可用回调
     */
    public final void setSurfaceAvailableCallback(@org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function2<? super android.view.Surface, ? super java.lang.String, kotlin.Unit> callback) {
    }
    
    /**
     * 设置变换变化回调
     */
    public final void setTransformChangeCallback(@org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function5<? super java.lang.String, ? super java.lang.Float, ? super java.lang.Float, ? super java.lang.Float, ? super java.lang.Float, kotlin.Unit> callback) {
    }
    
    /**
     * 设置裁剪模式变化回调
     */
    public final void setCropModeChangeCallback(@org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super java.lang.Boolean, kotlin.Unit> callback) {
    }
    
    /**
     * 🎯 新增：设置裁剪模式变化回调（包含连接ID）
     */
    public final void setOnCropModeChangeListener(@org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function2<? super java.lang.String, ? super java.lang.Boolean, kotlin.Unit> listener) {
    }
    
    /**
     * 通过拖动手柄执行拖动（供TextWindowManager调用）
     */
    public final void performDragByHandle(float deltaX, float deltaY) {
    }
    
    /**
     * 通知拖动结束（供TextWindowManager调用）
     */
    public final void notifyDragEnd() {
    }
    
    public final void setDragEnabled(boolean enabled) {
    }
    
    public final void setScaleEnabled(boolean enabled) {
    }
    
    public final void setRotationEnabled(boolean enabled) {
    }
    
    public final void setMirrorEnabled(boolean enabled) {
    }
    
    public final boolean isDragEnabled() {
        return false;
    }
    
    public final boolean isScaleEnabled() {
        return false;
    }
    
    public final boolean isRotationEnabled() {
        return false;
    }
    
    public final boolean isMirrorEnabled() {
        return false;
    }
    
    /**
     * 获取连接ID
     */
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getConnectionId() {
        return null;
    }
    
    /**
     * 📝 获取文本窗口管理器
     */
    @org.jetbrains.annotations.Nullable()
    public final com.example.castapp.ui.windowsettings.TextWindowManager getTextWindowManager() {
        return null;
    }
    
    /**
     * 🎬 获取媒体Surface管理器
     */
    @org.jetbrains.annotations.Nullable()
    public final com.example.castapp.ui.windowsettings.MediaSurfaceManager getMediaSurfaceManager() {
        return null;
    }
    
    public final float getActualDisplayX() {
        return 0.0F;
    }
    
    public final float getActualDisplayY() {
        return 0.0F;
    }
    
    public final float getContainerDisplayX() {
        return 0.0F;
    }
    
    public final float getContainerDisplayY() {
        return 0.0F;
    }
    
    public final float getCurrentScaleFactor() {
        return 0.0F;
    }
    
    public final float getCurrentRotation() {
        return 0.0F;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final android.view.View getContainerView() {
        return null;
    }
    
    public final void setPrecisionTransform(float x, float y, float scale, float rotation) {
    }
    
    public final void resetTransform() {
    }
    
    public final void startCropMode() {
    }
    
    public final void endCropMode(boolean isCancel) {
    }
    
    public final boolean isCroppingMode() {
        return false;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final android.graphics.RectF getCropRectRatio() {
        return null;
    }
    
    public final void setCropRectRatio(@org.jetbrains.annotations.Nullable()
    android.graphics.RectF rectRatio) {
    }
    
    public final void setCornerRadius(float radius) {
    }
    
    public final float getCornerRadius() {
        return 0.0F;
    }
    
    public final void setWindowAlpha(float alphaValue) {
    }
    
    public final float getWindowAlpha() {
        return 0.0F;
    }
    
    public final void setBorderEnabled(boolean enabled) {
    }
    
    public final boolean isBorderEnabled() {
        return false;
    }
    
    public final void setBorderColor(int color) {
    }
    
    public final int getBorderColor() {
        return 0;
    }
    
    public final void setBorderWidth(float width) {
    }
    
    public final float getBorderWidth() {
        return 0.0F;
    }
    
    /**
     * 🎯 统一边框法：重写bringToFront方法，确保边框视图同步调整层级
     */
    @java.lang.Override()
    public void bringToFront() {
    }
    
    /**
     * 🎯 统一边框法：重写setVisibility方法，确保边框视图同步显示/隐藏
     */
    @java.lang.Override()
    public void setVisibility(int visibility) {
    }
    
    public final int getBaseWindowWidth() {
        return 0;
    }
    
    public final int getBaseWindowHeight() {
        return 0;
    }
    
    /**
     * 更新基础窗口尺寸（主要用于文本窗口尺寸变化）
     * 🎯 统一边框法：移除对TransformRenderer原始尺寸的更新，因为不再需要容器扩展
     */
    public final void updateBaseWindowSize(int newWidth, int newHeight) {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final android.graphics.Bitmap captureScreenshot(boolean forRemoteControl) {
        return null;
    }
    
    /**
     * 检查触摸点是否在裁剪后的可见区域内
     * @param x 触摸点X坐标（相对于TransformHandler容器）
     * @param y 触摸点Y坐标（相对于TransformHandler容器）
     * @return true表示在可见区域内，false表示在被裁剪区域内
     */
    private final boolean isTouchInVisibleArea(float x, float y) {
        return false;
    }
    
    /**
     * 检测触摸点是否在边框区域
     */
    private final com.example.castapp.ui.windowsettings.TransformHandler.ResizeMode detectResizeMode(float x, float y) {
        return null;
    }
    
    /**
     * 执行边框调整大小
     */
    private final void performResize(float deltaX, float deltaY) {
    }
    
    /**
     * 🎯 关键修复：文本窗口尺寸调整后更新统一边框
     * 当文本窗口通过拖动手柄调整尺寸时，需要同步更新边框视图
     */
    public final void updateUnifiedBorderAfterResize() {
    }
    
    public final void cleanup() {
    }
    
    /**
     * 设置视频方向并应用变换
     */
    public final void setVideoOrientation(int orientation, int videoWidth, int videoHeight) {
    }
    
    /**
     * 获取方向名称（用于日志）
     */
    private final java.lang.String getOrientationName(int orientation) {
        return null;
    }
    
    @kotlin.jvm.JvmOverloads()
    public TransformHandler(@org.jetbrains.annotations.NotNull()
    android.content.Context context) {
        super(null);
    }
    
    @kotlin.jvm.JvmOverloads()
    public TransformHandler(@org.jetbrains.annotations.NotNull()
    android.content.Context context, @org.jetbrains.annotations.Nullable()
    android.util.AttributeSet attrs) {
        super(null);
    }
    
    @kotlin.jvm.JvmOverloads()
    public TransformHandler(@org.jetbrains.annotations.NotNull()
    android.content.Context context, @org.jetbrains.annotations.Nullable()
    android.util.AttributeSet attrs, int defStyleAttr) {
        super(null);
    }
    
    @kotlin.jvm.JvmOverloads()
    public TransformHandler(@org.jetbrains.annotations.NotNull()
    android.content.Context context, @org.jetbrains.annotations.Nullable()
    android.util.AttributeSet attrs, int defStyleAttr, int initialWidth) {
        super(null);
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0014\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0002\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0006"}, d2 = {"Lcom/example/castapp/ui/windowsettings/TransformHandler$Companion;", "", "()V", "DEFAULT_WINDOW_HEIGHT", "", "DEFAULT_WINDOW_WIDTH", "app_debug"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0010\u0010\n\u0002\b\u000b\b\u0082\u0081\u0002\u0018\u00002\b\u0012\u0004\u0012\u00020\u00000\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002j\u0002\b\u0003j\u0002\b\u0004j\u0002\b\u0005j\u0002\b\u0006j\u0002\b\u0007j\u0002\b\bj\u0002\b\tj\u0002\b\nj\u0002\b\u000b\u00a8\u0006\f"}, d2 = {"Lcom/example/castapp/ui/windowsettings/TransformHandler$ResizeMode;", "", "(Ljava/lang/String;I)V", "NONE", "LEFT", "RIGHT", "TOP", "BOTTOM", "TOP_LEFT", "TOP_RIGHT", "BOTTOM_LEFT", "BOTTOM_RIGHT", "app_debug"})
    static enum ResizeMode {
        /*public static final*/ NONE /* = new NONE() */,
        /*public static final*/ LEFT /* = new LEFT() */,
        /*public static final*/ RIGHT /* = new RIGHT() */,
        /*public static final*/ TOP /* = new TOP() */,
        /*public static final*/ BOTTOM /* = new BOTTOM() */,
        /*public static final*/ TOP_LEFT /* = new TOP_LEFT() */,
        /*public static final*/ TOP_RIGHT /* = new TOP_RIGHT() */,
        /*public static final*/ BOTTOM_LEFT /* = new BOTTOM_LEFT() */,
        /*public static final*/ BOTTOM_RIGHT /* = new BOTTOM_RIGHT() */;
        
        ResizeMode() {
        }
        
        @org.jetbrains.annotations.NotNull()
        public static kotlin.enums.EnumEntries<com.example.castapp.ui.windowsettings.TransformHandler.ResizeMode> getEntries() {
            return null;
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000*\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u0007\n\u0002\b\u0006\n\u0002\u0010\u000b\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0003\b\u0002\u0018\u00002\u00020\u0001:\u0001\u0013B\r\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J\u0010\u0010\u000f\u001a\u00020\u00062\u0006\u0010\u0010\u001a\u00020\u0011H\u0002J\u000e\u0010\u0012\u001a\u00020\r2\u0006\u0010\u0010\u001a\u00020\u0011R\u001e\u0010\u0007\u001a\u00020\u00062\u0006\u0010\u0005\u001a\u00020\u0006@BX\u0086\u000e\u00a2\u0006\b\n\u0000\u001a\u0004\b\b\u0010\tR\u001e\u0010\n\u001a\u00020\u00062\u0006\u0010\u0005\u001a\u00020\u0006@BX\u0086\u000e\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000b\u0010\tR\u000e\u0010\f\u001a\u00020\rX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000e\u001a\u00020\u0006X\u0082\u000e\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0014"}, d2 = {"Lcom/example/castapp/ui/windowsettings/TransformHandler$RotationGestureDetector;", "", "listener", "Lcom/example/castapp/ui/windowsettings/TransformHandler$RotationGestureDetector$OnRotationGestureListener;", "(Lcom/example/castapp/ui/windowsettings/TransformHandler$RotationGestureDetector$OnRotationGestureListener;)V", "<set-?>", "", "focusX", "getFocusX", "()F", "focusY", "getFocusY", "isInProgress", "", "prevAngle", "getAngle", "event", "Landroid/view/MotionEvent;", "onTouchEvent", "OnRotationGestureListener", "app_debug"})
    static final class RotationGestureDetector {
        @org.jetbrains.annotations.NotNull()
        private final com.example.castapp.ui.windowsettings.TransformHandler.RotationGestureDetector.OnRotationGestureListener listener = null;
        private float prevAngle = 0.0F;
        private boolean isInProgress = false;
        private float focusX = 0.0F;
        private float focusY = 0.0F;
        
        public RotationGestureDetector(@org.jetbrains.annotations.NotNull()
        com.example.castapp.ui.windowsettings.TransformHandler.RotationGestureDetector.OnRotationGestureListener listener) {
            super();
        }
        
        public final float getFocusX() {
            return 0.0F;
        }
        
        public final float getFocusY() {
            return 0.0F;
        }
        
        public final boolean onTouchEvent(@org.jetbrains.annotations.NotNull()
        android.view.MotionEvent event) {
            return false;
        }
        
        private final float getAngle(android.view.MotionEvent event) {
            return 0.0F;
        }
        
        @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000$\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000b\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0007\n\u0002\b\u0002\n\u0002\u0010\u0002\n\u0000\bf\u0018\u00002\u00020\u0001J\u0018\u0010\u0002\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u00052\u0006\u0010\u0006\u001a\u00020\u0007H&J\u0010\u0010\b\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u0005H&J\u0010\u0010\t\u001a\u00020\n2\u0006\u0010\u0004\u001a\u00020\u0005H&\u00a8\u0006\u000b"}, d2 = {"Lcom/example/castapp/ui/windowsettings/TransformHandler$RotationGestureDetector$OnRotationGestureListener;", "", "onRotate", "", "detector", "Lcom/example/castapp/ui/windowsettings/TransformHandler$RotationGestureDetector;", "rotationDelta", "", "onRotationBegin", "onRotationEnd", "", "app_debug"})
        public static abstract interface OnRotationGestureListener {
            
            public abstract boolean onRotationBegin(@org.jetbrains.annotations.NotNull()
            com.example.castapp.ui.windowsettings.TransformHandler.RotationGestureDetector detector);
            
            public abstract boolean onRotate(@org.jetbrains.annotations.NotNull()
            com.example.castapp.ui.windowsettings.TransformHandler.RotationGestureDetector detector, float rotationDelta);
            
            public abstract void onRotationEnd(@org.jetbrains.annotations.NotNull()
            com.example.castapp.ui.windowsettings.TransformHandler.RotationGestureDetector detector);
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000&\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000b\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0007\n\u0002\b\u0002\n\u0002\u0010\u0002\n\u0000\b\u0082\u0004\u0018\u00002\u00020\u0001B\u0005\u00a2\u0006\u0002\u0010\u0002J\u0018\u0010\u0003\u001a\u00020\u00042\u0006\u0010\u0005\u001a\u00020\u00062\u0006\u0010\u0007\u001a\u00020\bH\u0016J\u0010\u0010\t\u001a\u00020\u00042\u0006\u0010\u0005\u001a\u00020\u0006H\u0016J\u0010\u0010\n\u001a\u00020\u000b2\u0006\u0010\u0005\u001a\u00020\u0006H\u0016\u00a8\u0006\f"}, d2 = {"Lcom/example/castapp/ui/windowsettings/TransformHandler$RotationGestureListener;", "Lcom/example/castapp/ui/windowsettings/TransformHandler$RotationGestureDetector$OnRotationGestureListener;", "(Lcom/example/castapp/ui/windowsettings/TransformHandler;)V", "onRotate", "", "detector", "Lcom/example/castapp/ui/windowsettings/TransformHandler$RotationGestureDetector;", "rotationDelta", "", "onRotationBegin", "onRotationEnd", "", "app_debug"})
    final class RotationGestureListener implements com.example.castapp.ui.windowsettings.TransformHandler.RotationGestureDetector.OnRotationGestureListener {
        
        public RotationGestureListener() {
            super();
        }
        
        @java.lang.Override()
        public boolean onRotationBegin(@org.jetbrains.annotations.NotNull()
        com.example.castapp.ui.windowsettings.TransformHandler.RotationGestureDetector detector) {
            return false;
        }
        
        @java.lang.Override()
        public boolean onRotate(@org.jetbrains.annotations.NotNull()
        com.example.castapp.ui.windowsettings.TransformHandler.RotationGestureDetector detector, float rotationDelta) {
            return false;
        }
        
        @java.lang.Override()
        public void onRotationEnd(@org.jetbrains.annotations.NotNull()
        com.example.castapp.ui.windowsettings.TransformHandler.RotationGestureDetector detector) {
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000 \n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000b\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u0002\n\u0000\b\u0082\u0004\u0018\u00002\u00020\u0001B\u0005\u00a2\u0006\u0002\u0010\u0002J\u0010\u0010\u0003\u001a\u00020\u00042\u0006\u0010\u0005\u001a\u00020\u0006H\u0016J\u0010\u0010\u0007\u001a\u00020\u00042\u0006\u0010\u0005\u001a\u00020\u0006H\u0016J\u0010\u0010\b\u001a\u00020\t2\u0006\u0010\u0005\u001a\u00020\u0006H\u0016\u00a8\u0006\n"}, d2 = {"Lcom/example/castapp/ui/windowsettings/TransformHandler$ScaleGestureListener;", "Landroid/view/ScaleGestureDetector$SimpleOnScaleGestureListener;", "(Lcom/example/castapp/ui/windowsettings/TransformHandler;)V", "onScale", "", "detector", "Landroid/view/ScaleGestureDetector;", "onScaleBegin", "onScaleEnd", "", "app_debug"})
    final class ScaleGestureListener extends android.view.ScaleGestureDetector.SimpleOnScaleGestureListener {
        
        public ScaleGestureListener() {
            super();
        }
        
        @java.lang.Override()
        public boolean onScaleBegin(@org.jetbrains.annotations.NotNull()
        android.view.ScaleGestureDetector detector) {
            return false;
        }
        
        @java.lang.Override()
        public boolean onScale(@org.jetbrains.annotations.NotNull()
        android.view.ScaleGestureDetector detector) {
            return false;
        }
        
        @java.lang.Override()
        public void onScaleEnd(@org.jetbrains.annotations.NotNull()
        android.view.ScaleGestureDetector detector) {
        }
    }
}