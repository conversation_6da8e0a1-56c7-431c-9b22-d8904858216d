package com.example.castapp.ui.adapter

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.SeekBar
import android.widget.TextView
import androidx.appcompat.app.AlertDialog
import androidx.appcompat.widget.SwitchCompat
import androidx.core.content.edit
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView

import com.example.castapp.R
import com.example.castapp.model.CastWindowInfo
import com.example.castapp.utils.AppLog


/**
 * 窗口管理器RecyclerView适配器
 */
class WindowManagerAdapter : ListAdapter<CastWindowInfo, WindowManagerAdapter.WindowViewHolder>(WindowDiffCallback()) {



    // 裁剪开关回调接口
    interface OnCropSwitchListener {
        fun onCropSwitchChanged(connectionId: String, isEnabled: Boolean)
    }

    // 变换功能开关回调接口
    interface OnTransformSwitchListener {
        fun onDragSwitchChanged(connectionId: String, isEnabled: Boolean)
        fun onScaleSwitchChanged(connectionId: String, isEnabled: Boolean)
        fun onRotationSwitchChanged(connectionId: String, isEnabled: Boolean)
    }

    // 窗口可见性开关回调接口
    interface OnVisibilitySwitchListener {
        fun onVisibilitySwitchChanged(connectionId: String, isVisible: Boolean)
    }

    interface OnMirrorSwitchListener {
        fun onMirrorSwitchChanged(connectionId: String, isEnabled: Boolean)
    }

    interface OnCornerRadiusChangeListener {
        fun onCornerRadiusChanged(connectionId: String, cornerRadius: Float)
    }

    interface OnAlphaChangeListener {
        fun onAlphaChanged(connectionId: String, alpha: Float)
    }

    interface OnControlSwitchListener {
        fun onControlSwitchChanged(connectionId: String, isEnabled: Boolean)
    }



    interface OnBorderSwitchListener {
        fun onBorderSwitchChanged(connectionId: String, isEnabled: Boolean)
    }

    interface OnBorderColorChangeListener {
        fun onBorderColorChanged(connectionId: String, color: Int)
    }

    interface OnBorderWidthChangeListener {
        fun onBorderWidthChanged(connectionId: String, width: Float)
    }

    // 🏷️ 备注变更回调接口
    interface OnNoteChangeListener {
        fun onNoteChanged(connectionId: String, note: String)
    }

    interface OnWindowDeleteListener {
        fun onWindowDelete(windowInfo: CastWindowInfo)
    }

    // 视频播放控制回调接口
    interface OnVideoControlListener {
        fun onVideoPlaySwitchChanged(connectionId: String, isEnabled: Boolean)
        fun onVideoLoopCountChanged(connectionId: String, loopCount: Int)
        fun onVideoVolumeChanged(connectionId: String, volume: Int)
    }

    // 🎯 横屏模式控制回调接口
    interface OnLandscapeSwitchListener {
        fun onLandscapeSwitchChanged(connectionId: String, isEnabled: Boolean)
    }

    // 📝 编辑模式控制回调接口（仅文字窗口）
    interface OnEditSwitchListener {
        fun onEditSwitchChanged(connectionId: String, isEnabled: Boolean)
    }


    // 🎯 根源性修复：改为internal，让ViewHolder可以动态访问当前监听器
    internal var onCropSwitchListener: OnCropSwitchListener? = null
    internal var onTransformSwitchListener: OnTransformSwitchListener? = null
    internal var onVisibilitySwitchListener: OnVisibilitySwitchListener? = null
    internal var onMirrorSwitchListener: OnMirrorSwitchListener? = null
    internal var onCornerRadiusChangeListener: OnCornerRadiusChangeListener? = null
    internal var onAlphaChangeListener: OnAlphaChangeListener? = null
    internal var onControlSwitchListener: OnControlSwitchListener? = null

    internal var onBorderSwitchListener: OnBorderSwitchListener? = null
    internal var onBorderColorChangeListener: OnBorderColorChangeListener? = null
    internal var onBorderWidthChangeListener: OnBorderWidthChangeListener? = null
    internal var onNoteChangeListener: OnNoteChangeListener? = null // 🏷️ 备注变更监听器
    internal var onWindowDeleteListener: OnWindowDeleteListener? = null // 🗑️ 窗口删除监听器
    internal var onVideoControlListener: OnVideoControlListener? = null // 🎬 视频控制监听器
    internal var onLandscapeSwitchListener: OnLandscapeSwitchListener? = null // 🎯 横屏模式控制监听器
    internal var onEditSwitchListener: OnEditSwitchListener? = null // 📝 编辑模式控制监听器
    private val internalList = mutableListOf<CastWindowInfo>()

    // 🪟 远程模式标志（只读模式）
    private var isRemoteMode = false



    fun setOnCropSwitchListener(listener: OnCropSwitchListener?) {
        this.onCropSwitchListener = listener
    }

    fun setOnTransformSwitchListener(listener: OnTransformSwitchListener?) {
        this.onTransformSwitchListener = listener
    }

    fun setOnVisibilitySwitchListener(listener: OnVisibilitySwitchListener?) {
        this.onVisibilitySwitchListener = listener
    }

    fun setOnMirrorSwitchListener(listener: OnMirrorSwitchListener?) {
        onMirrorSwitchListener = listener
    }

    fun setOnCornerRadiusChangeListener(listener: OnCornerRadiusChangeListener?) {
        onCornerRadiusChangeListener = listener
    }

    fun setOnAlphaChangeListener(listener: OnAlphaChangeListener?) {
        onAlphaChangeListener = listener
    }

    fun setOnControlSwitchListener(listener: OnControlSwitchListener?) {
        onControlSwitchListener = listener
    }



    fun setOnBorderSwitchListener(listener: OnBorderSwitchListener?) {
        onBorderSwitchListener = listener
    }

    fun setOnBorderColorChangeListener(listener: OnBorderColorChangeListener?) {
        onBorderColorChangeListener = listener
    }

    fun setOnBorderWidthChangeListener(listener: OnBorderWidthChangeListener?) {
        onBorderWidthChangeListener = listener
    }

    // 🏷️ 设置备注变更监听器
    fun setOnNoteChangeListener(listener: OnNoteChangeListener?) {
        onNoteChangeListener = listener
    }

    fun setOnWindowDeleteListener(listener: OnWindowDeleteListener?) {
        onWindowDeleteListener = listener
    }

    // 🎬 设置视频控制监听器
    fun setOnVideoControlListener(listener: OnVideoControlListener?) {
        onVideoControlListener = listener
    }

    // 🎯 设置横屏模式控制监听器
    fun setOnLandscapeSwitchListener(listener: OnLandscapeSwitchListener?) {
        onLandscapeSwitchListener = listener
    }

    // 📝 设置编辑模式控制监听器
    fun setOnEditSwitchListener(listener: OnEditSwitchListener?) {
        onEditSwitchListener = listener
    }

    /**
     * 🪟 设置远程模式（只读模式）
     */
    fun setRemoteMode(isRemote: Boolean) {
        this.isRemoteMode = isRemote
        AppLog.d("【适配器】设置远程模式: $isRemote")

        // 🔧 修复：不再调用notifyDataSetChanged()，避免重置用户的UI操作状态
        // 远程模式的变更只影响监听器的设置，不应该重置开关状态
        AppLog.d("【适配器】远程模式变更，保持当前UI状态")
    }

    /**
     * 🔧 更新所有ViewHolder的监听器设置（不重置UI状态）
     */
    fun updateListenersOnly() {
        // 通过payload方式通知ViewHolder只更新监听器
        notifyItemRangeChanged(0, itemCount, "UPDATE_LISTENERS_ONLY")
        AppLog.d("【适配器】已通知更新监听器设置，不重置UI状态")
    }

    override fun submitList(list: List<CastWindowInfo>?) {
        AppLog.d("【适配器】submitList调用，新列表大小: ${list?.size ?: 0}")
        list?.forEachIndexed { index, windowInfo ->
            AppLog.d("【适配器】新列表 位置${index} -> 序号${index + 1}: ${windowInfo.getDisplayTextWithDevice()}")
        }

        super.submitList(list)
        internalList.clear()
        list?.let { internalList.addAll(it) }

        AppLog.d("【适配器】internalList更新完成，大小: ${internalList.size}")
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): WindowViewHolder {
        val view = LayoutInflater.from(parent.context)
            .inflate(R.layout.item_window_settings, parent, false)
        return WindowViewHolder(view)
    }

    override fun onBindViewHolder(holder: WindowViewHolder, position: Int) {
        holder.bind(getItem(position), position, isRemoteMode, onCropSwitchListener, onTransformSwitchListener, onVisibilitySwitchListener, onMirrorSwitchListener, onCornerRadiusChangeListener, onAlphaChangeListener, onControlSwitchListener, onBorderSwitchListener, onBorderColorChangeListener, onBorderWidthChangeListener, onNoteChangeListener, onWindowDeleteListener, onVideoControlListener, onLandscapeSwitchListener, onEditSwitchListener)
    }

    override fun onBindViewHolder(holder: WindowViewHolder, position: Int, payloads: MutableList<Any>) {
        if (payloads.isNotEmpty()) {
            when {
                payloads.contains("UPDATE_ORDER_NUMBER") -> {
                    // 🐾 只更新序号，避免整个ViewHolder重新绑定
                    holder.updateOrderNumber(position)
                    AppLog.d("【序号更新】只更新位置${position}的序号")
                }
                payloads.contains("UPDATE_LISTENERS_ONLY") -> {
                    // 🔧 只更新监听器设置，不重置UI状态
                    holder.updateListenersOnly(getItem(position), isRemoteMode, onCropSwitchListener, onTransformSwitchListener, onVisibilitySwitchListener, onMirrorSwitchListener, onCornerRadiusChangeListener, onAlphaChangeListener, onControlSwitchListener, onBorderSwitchListener, onBorderColorChangeListener, onBorderWidthChangeListener, onNoteChangeListener, onWindowDeleteListener, onVideoControlListener, onLandscapeSwitchListener, onEditSwitchListener)
                    AppLog.d("【监听器更新】只更新位置${position}的监听器设置")
                }
                else -> {
                    super.onBindViewHolder(holder, position, payloads)
                }
            }
        } else {
            super.onBindViewHolder(holder, position, payloads)
        }
    }



    class WindowViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        private val tvOrderNumber: TextView = itemView.findViewById(R.id.tv_order_number)
        private val tvIpAddress: TextView = itemView.findViewById(R.id.tv_ip_address)
        private val tvWindowSize: TextView = itemView.findViewById(R.id.tv_window_size)
        private val tvConnectionId: TextView = itemView.findViewById(R.id.tv_connection_id)
        private val tvDeviceName: TextView = itemView.findViewById(R.id.tv_device_name)
        private val tvDeviceNote: TextView = itemView.findViewById(R.id.tv_device_note) // 🏷️ 设备备注
        private val tvTransformInfo: TextView = itemView.findViewById(R.id.tv_transform_info)
        private val switchCrop: SwitchCompat = itemView.findViewById(R.id.switch_crop)
        private val switchDrag: SwitchCompat = itemView.findViewById(R.id.switch_drag)
        private val switchScale: SwitchCompat = itemView.findViewById(R.id.switch_scale)
        private val switchRotation: SwitchCompat = itemView.findViewById(R.id.switch_rotation)
        private val switchVisible: SwitchCompat = itemView.findViewById(R.id.switch_visible)
        private val switchMirror: SwitchCompat = itemView.findViewById(R.id.switch_mirror)
        private val switchControl: SwitchCompat = itemView.findViewById(R.id.switch_control)

        private val switchBorder: SwitchCompat = itemView.findViewById(R.id.switch_border)
        private val switchLandscape: SwitchCompat = itemView.findViewById(R.id.switch_landscape) // 🎯 横屏开关
        private val switchEdit: SwitchCompat = itemView.findViewById(R.id.switch_edit) // 📝 编辑开关
        private val ivColorPalette: ImageView = itemView.findViewById(R.id.iv_color_palette)
        private val seekbarBorderWidth: SeekBar = itemView.findViewById(R.id.seekbar_border_width)
        private val tvBorderWidthValue: TextView = itemView.findViewById(R.id.tv_border_width_value)
        private val seekbarCornerRadius: SeekBar = itemView.findViewById(R.id.seekbar_corner_radius)
        private val tvCornerRadiusValue: TextView = itemView.findViewById(R.id.tv_corner_radius_value)
        private val seekbarAlpha: SeekBar = itemView.findViewById(R.id.seekbar_alpha)
        private val tvAlphaValue: TextView = itemView.findViewById(R.id.tv_alpha_value)
        private val ivDeleteWindow: ImageView = itemView.findViewById(R.id.iv_delete_window) // 🗑️ 删除图标

        // 🎬 视频控制相关视图
        private val layoutVideoControls: View = itemView.findViewById(R.id.layout_video_controls)
        private val switchVideoPlay: SwitchCompat = itemView.findViewById(R.id.switch_video_play)
        private val btnVideoLoopCount: android.widget.Button = itemView.findViewById(R.id.btn_video_loop_count)
        private val seekbarVideoVolume: SeekBar = itemView.findViewById(R.id.seekbar_video_volume)
        private val tvVideoVolumeValue: TextView = itemView.findViewById(R.id.tv_video_volume_value)

        // 🎯 根源性修复：动态获取当前监听器的方法，避免闭包捕获旧引用
        private fun getCurrentCropSwitchListener(): OnCropSwitchListener? {
            return (bindingAdapter as? WindowManagerAdapter)?.onCropSwitchListener
        }

        private fun getCurrentTransformSwitchListener(): OnTransformSwitchListener? {
            return (bindingAdapter as? WindowManagerAdapter)?.onTransformSwitchListener
        }

        private fun getCurrentVisibilitySwitchListener(): OnVisibilitySwitchListener? {
            return (bindingAdapter as? WindowManagerAdapter)?.onVisibilitySwitchListener
        }

        private fun getCurrentMirrorSwitchListener(): OnMirrorSwitchListener? {
            return (bindingAdapter as? WindowManagerAdapter)?.onMirrorSwitchListener
        }

        private fun getCurrentControlSwitchListener(): OnControlSwitchListener? {
            return (bindingAdapter as? WindowManagerAdapter)?.onControlSwitchListener
        }

        private fun getCurrentBorderSwitchListener(): OnBorderSwitchListener? {
            return (bindingAdapter as? WindowManagerAdapter)?.onBorderSwitchListener
        }

        fun bind(windowInfo: CastWindowInfo, position: Int, isRemoteMode: Boolean, onCropSwitchListener: OnCropSwitchListener?, onTransformSwitchListener: OnTransformSwitchListener?, onVisibilitySwitchListener: OnVisibilitySwitchListener?, onMirrorSwitchListener: OnMirrorSwitchListener?, onCornerRadiusChangeListener: OnCornerRadiusChangeListener?, onAlphaChangeListener: OnAlphaChangeListener?, onControlSwitchListener: OnControlSwitchListener?, onBorderSwitchListener: OnBorderSwitchListener?, onBorderColorChangeListener: OnBorderColorChangeListener?, onBorderWidthChangeListener: OnBorderWidthChangeListener?, onNoteChangeListener: OnNoteChangeListener?, onWindowDeleteListener: OnWindowDeleteListener?, onVideoControlListener: OnVideoControlListener?, onLandscapeSwitchListener: OnLandscapeSwitchListener?, onEditSwitchListener: OnEditSwitchListener?) {
            // 设置视觉层级序号（position 0 = 最上层 = 序号1）
            val layerNumber = position + 1
            tvOrderNumber.text = "$layerNumber. "
            AppLog.d("【序号显示】位置${position} -> 层级序号${layerNumber}: ${windowInfo.getDisplayTextWithDevice()}")

            // 设置设备名称 - 主要显示
            if (!windowInfo.deviceName.isNullOrBlank()) {
                tvDeviceName.text = windowInfo.deviceName
            } else {
                tvDeviceName.text = "未知设备"
            }

            // 设置连接ID - 次要信息
            tvConnectionId.text = "（ID:${windowInfo.getShortConnectionId()}）"

            // 🏷️ 设置备注 - 显示和点击处理
            tvDeviceNote.text = windowInfo.getNoteDisplayText()
            if (!isRemoteMode) {
                tvDeviceNote.setOnClickListener {
                    showNoteEditDialog(windowInfo, onNoteChangeListener)
                }
            } else {
                tvDeviceNote.setOnClickListener(null)
            }

            // 🗑️ 设置删除图标点击处理
            if (!isRemoteMode) {
                ivDeleteWindow.setOnClickListener {
                    showDeleteConfirmDialog(windowInfo, onWindowDeleteListener)
                }
                ivDeleteWindow.visibility = View.VISIBLE
            } else {
                ivDeleteWindow.setOnClickListener(null)
                ivDeleteWindow.visibility = View.GONE
            }

            // 设置IP地址和端口 - 辅助信息
            tvIpAddress.text = windowInfo.getDisplayAddress()

            // 设置窗口尺寸信息
            tvWindowSize.text = windowInfo.getWindowSizeInfo()

            // 设置变换信息 - 位置、缩放、旋转
            tvTransformInfo.text = windowInfo.getTransformInfo()

            // 🎬 视频控制区域显示逻辑
            val isVideoWindow = windowInfo.connectionId.startsWith("video_")
            if (isVideoWindow) {
                layoutVideoControls.visibility = View.VISIBLE
                setupVideoControls(windowInfo, isRemoteMode, onVideoControlListener)
            } else {
                layoutVideoControls.visibility = View.GONE
            }

            // 🪟 远程模式处理：禁用所有交互控件但保持界面完整性
            if (isRemoteMode) {
                // 禁用所有开关和滑动条
                switchDrag.isEnabled = false
                switchScale.isEnabled = false
                switchRotation.isEnabled = false
                switchCrop.isEnabled = false
                switchVisible.isEnabled = false
                switchMirror.isEnabled = false
                switchControl.isEnabled = false
                switchLandscape.isEnabled = false
                switchEdit.isEnabled = false

                switchBorder.isEnabled = false
                ivColorPalette.isClickable = false
                seekbarBorderWidth.isEnabled = false
                seekbarCornerRadius.isEnabled = false
                seekbarAlpha.isEnabled = false

                // 禁用视频控制
                if (isVideoWindow) {
                    switchVideoPlay.isEnabled = false
                    btnVideoLoopCount.isEnabled = false
                    seekbarVideoVolume.isEnabled = false
                }

                AppLog.d("【远程模式】禁用所有交互控件但保持界面完整性: ${windowInfo.connectionId}")
            } else {
                // 启用所有控件（本地模式）
                switchDrag.isEnabled = true
                switchScale.isEnabled = true
                switchRotation.isEnabled = true
                switchCrop.isEnabled = true
                switchVisible.isEnabled = true
                switchMirror.isEnabled = true
                switchControl.isEnabled = true
                switchLandscape.isEnabled = true
                switchEdit.isEnabled = true

                switchBorder.isEnabled = true
                ivColorPalette.isClickable = true
                seekbarCornerRadius.isEnabled = true
                seekbarAlpha.isEnabled = true

                // 启用视频控制
                if (isVideoWindow) {
                    switchVideoPlay.isEnabled = true
                    btnVideoLoopCount.isEnabled = true
                    seekbarVideoVolume.isEnabled = true
                }

            }

            // 设置变换功能开关
            switchDrag.isChecked = windowInfo.isDragEnabled
            if (!isRemoteMode) {
                switchDrag.setOnCheckedChangeListener { _, isChecked ->
                    // 🎯 根源性修复：动态获取当前监听器，避免闭包捕获旧引用
                    getCurrentTransformSwitchListener()?.onDragSwitchChanged(windowInfo.connectionId, isChecked)
                }
            } else {
                switchDrag.setOnCheckedChangeListener(null)
            }

            switchScale.isChecked = windowInfo.isScaleEnabled
            if (!isRemoteMode) {
                switchScale.setOnCheckedChangeListener { _, isChecked ->
                    // 🎯 根源性修复：动态获取当前监听器，避免闭包捕获旧引用
                    getCurrentTransformSwitchListener()?.onScaleSwitchChanged(windowInfo.connectionId, isChecked)
                }
            } else {
                switchScale.setOnCheckedChangeListener(null)
            }

            switchRotation.isChecked = windowInfo.isRotationEnabled
            if (!isRemoteMode) {
                switchRotation.setOnCheckedChangeListener { _, isChecked ->
                    // 🎯 根源性修复：动态获取当前监听器，避免闭包捕获旧引用
                    getCurrentTransformSwitchListener()?.onRotationSwitchChanged(windowInfo.connectionId, isChecked)
                }
            } else {
                switchRotation.setOnCheckedChangeListener(null)
            }

            // 设置裁剪开关
            switchCrop.isChecked = windowInfo.isCropping
            if (!isRemoteMode) {
                switchCrop.setOnCheckedChangeListener { _, isChecked ->
                    // 🎯 根源性修复：动态获取当前监听器，避免闭包捕获旧引用
                    getCurrentCropSwitchListener()?.onCropSwitchChanged(windowInfo.connectionId, isChecked)
                }
            } else {
                switchCrop.setOnCheckedChangeListener(null)
            }

            // 设置隐藏开关
            switchVisible.isChecked = windowInfo.isVisible
            if (!isRemoteMode) {
                switchVisible.setOnCheckedChangeListener { _, isChecked ->
                    // 🎯 根源性修复：动态获取当前监听器，避免闭包捕获旧引用
                    getCurrentVisibilitySwitchListener()?.onVisibilitySwitchChanged(windowInfo.connectionId, isChecked)
                }
            } else {
                switchVisible.setOnCheckedChangeListener(null)
            }

            // 设置镜像开关
            switchMirror.isChecked = windowInfo.isMirrored
            if (!isRemoteMode) {
                switchMirror.setOnCheckedChangeListener { _, isChecked ->
                    // 🎯 根源性修复：动态获取当前监听器，避免闭包捕获旧引用
                    getCurrentMirrorSwitchListener()?.onMirrorSwitchChanged(windowInfo.connectionId, isChecked)
                }
            } else {
                switchMirror.setOnCheckedChangeListener(null)
            }

            // 设置调控开关
            switchControl.isChecked = windowInfo.isControlEnabled
            if (!isRemoteMode) {
                switchControl.setOnCheckedChangeListener { _, isChecked ->
                    // 🎯 根源性修复：动态获取当前监听器，避免闭包捕获旧引用
                    getCurrentControlSwitchListener()?.onControlSwitchChanged(windowInfo.connectionId, isChecked)
                }
            } else {
                switchControl.setOnCheckedChangeListener(null)
            }



            // 设置边框开关
            switchBorder.isChecked = windowInfo.isBorderEnabled
            if (!isRemoteMode) {
                switchBorder.setOnCheckedChangeListener { _, isChecked ->
                    // 🎯 根源性修复：动态获取当前监听器，避免闭包捕获旧引用
                    getCurrentBorderSwitchListener()?.onBorderSwitchChanged(windowInfo.connectionId, isChecked)
                    // 🎨 根据边框开关状态更新色板图标
                    updateColorPaletteState(isChecked)
                    // 🎯 根据边框开关状态更新边框宽度滑动条
                    updateBorderWidthState(isChecked)
                }
            } else {
                switchBorder.setOnCheckedChangeListener(null)
            }

            // 🎨 设置色板图标
            updateColorPaletteState(windowInfo.isBorderEnabled)
            if (!isRemoteMode) {
                ivColorPalette.setOnClickListener {
                    AppLog.d("🎨 色板图标被点击: ${windowInfo.connectionId}")
                    if (switchBorder.isChecked) {
                        AppLog.d("🎨 边框开关已开启，准备显示颜色选择对话框")
                        showColorPickerDialog(windowInfo.connectionId, windowInfo.borderColor, onBorderColorChangeListener)
                    } else {
                        AppLog.d("🎨 边框开关未开启，忽略点击")
                    }
                }
            } else {
                ivColorPalette.setOnClickListener(null)
            }

            // 🎯 设置边框宽度滑动条
            val borderWidthInt = windowInfo.borderWidth.toInt()
            seekbarBorderWidth.progress = borderWidthInt - 1 // SeekBar从0开始，边框宽度从1开始
            tvBorderWidthValue.text = "${borderWidthInt}dp"

            // 根据边框开关状态更新边框宽度滑动条状态
            updateBorderWidthState(windowInfo.isBorderEnabled)

            if (!isRemoteMode) {
                seekbarBorderWidth.setOnSeekBarChangeListener(object : SeekBar.OnSeekBarChangeListener {
                    override fun onProgressChanged(seekBar: SeekBar?, progress: Int, fromUser: Boolean) {
                        if (fromUser) {
                            val borderWidth = progress + 1f // 转换为1-20dp范围
                            tvBorderWidthValue.text = "${borderWidth.toInt()}dp"
                            onBorderWidthChangeListener?.onBorderWidthChanged(windowInfo.connectionId, borderWidth)
                        }
                    }
                    override fun onStartTrackingTouch(seekBar: SeekBar?) {}
                    override fun onStopTrackingTouch(seekBar: SeekBar?) {}
                })
            } else {
                seekbarBorderWidth.setOnSeekBarChangeListener(null)
            }

            // 🎯 横屏开关：只对投屏窗口显示和生效
            val isCastWindow = !windowInfo.connectionId.let { id ->
                id == "front_camera" || id == "rear_camera" ||
                id.startsWith("video_") || id.startsWith("image_") ||
                id.startsWith("text_")
            }

            if (isCastWindow) {
                // 投屏窗口：显示横屏开关
                switchLandscape.visibility = View.VISIBLE
                switchLandscape.isChecked = windowInfo.isLandscapeModeEnabled
                if (!isRemoteMode) {
                    switchLandscape.setOnCheckedChangeListener { _, isChecked ->
                        onLandscapeSwitchListener?.onLandscapeSwitchChanged(windowInfo.connectionId, isChecked)
                    }
                } else {
                    switchLandscape.setOnCheckedChangeListener(null)
                }
            } else {
                // 其他窗口：隐藏横屏开关
                switchLandscape.visibility = View.GONE
                switchLandscape.setOnCheckedChangeListener(null)
            }

            // 📝 编辑开关：只对文字窗口显示和生效
            val isTextWindow = windowInfo.connectionId.startsWith("text_")
            if (isTextWindow) {
                // 文字窗口：显示编辑开关
                switchEdit.visibility = View.VISIBLE

                // 🔧 修复：先清除监听器，避免设置状态时触发回调
                switchEdit.setOnCheckedChangeListener(null)

                // 从WindowInfo获取编辑状态
                val editState = windowInfo.isEditEnabled
                switchEdit.isChecked = editState
                AppLog.d("【编辑开关绑定】设置编辑开关状态: ${windowInfo.connectionId} -> $editState")

                // 🔧 修复：设置状态后再设置监听器
                if (!isRemoteMode) {
                    switchEdit.setOnCheckedChangeListener { _, isChecked ->
                        AppLog.d("【编辑开关】用户操作编辑开关: ${windowInfo.connectionId} -> $isChecked")
                        onEditSwitchListener?.onEditSwitchChanged(windowInfo.connectionId, isChecked)
                    }
                }
            } else {
                // 其他窗口：隐藏编辑开关
                switchEdit.visibility = View.GONE
                switchEdit.setOnCheckedChangeListener(null)
            }

            // 设置圆角滑动条
            val cornerRadiusInt = windowInfo.cornerRadius.toInt()
            seekbarCornerRadius.progress = cornerRadiusInt
            tvCornerRadiusValue.text = "${cornerRadiusInt}dp"

            if (!isRemoteMode) {
                seekbarCornerRadius.setOnSeekBarChangeListener(object : SeekBar.OnSeekBarChangeListener {
                    override fun onProgressChanged(seekBar: SeekBar?, progress: Int, fromUser: Boolean) {
                        if (fromUser) {
                            tvCornerRadiusValue.text = "${progress}dp"
                            onCornerRadiusChangeListener?.onCornerRadiusChanged(windowInfo.connectionId, progress.toFloat())
                        }
                    }

                    override fun onStartTrackingTouch(seekBar: SeekBar?) {}
                    override fun onStopTrackingTouch(seekBar: SeekBar?) {}
                })
            } else {
                seekbarCornerRadius.setOnSeekBarChangeListener(null)
            }

            // 设置透明度滑动条
            val alphaPercent = (windowInfo.alpha * 100).toInt()
            seekbarAlpha.progress = alphaPercent
            tvAlphaValue.text = "${alphaPercent}%"

            if (!isRemoteMode) {
                seekbarAlpha.setOnSeekBarChangeListener(object : SeekBar.OnSeekBarChangeListener {
                    override fun onProgressChanged(seekBar: SeekBar?, progress: Int, fromUser: Boolean) {
                        if (fromUser) {
                            tvAlphaValue.text = "${progress}%"
                            val alphaValue = progress / 100.0f
                            onAlphaChangeListener?.onAlphaChanged(windowInfo.connectionId, alphaValue)
                        }
                    }

                    override fun onStartTrackingTouch(seekBar: SeekBar?) {}
                    override fun onStopTrackingTouch(seekBar: SeekBar?) {}
                })
            } else {
                seekbarAlpha.setOnSeekBarChangeListener(null)
            }
        }

        /**
         * 🐾 只更新序号，用于拖拽结束后的丝滑更新
         */
        fun updateOrderNumber(position: Int) {
            val layerNumber = position + 1
            tvOrderNumber.text = "$layerNumber. "
            AppLog.d("【序号更新】位置${position} -> 层级序号${layerNumber}")
        }

        /**
         * 🔧 只更新监听器设置，不重置UI状态
         */
        fun updateListenersOnly(
            windowInfo: CastWindowInfo,
            isRemoteMode: Boolean,
            onCropSwitchListener: OnCropSwitchListener?,
            onTransformSwitchListener: OnTransformSwitchListener?,
            onVisibilitySwitchListener: OnVisibilitySwitchListener?,
            onMirrorSwitchListener: OnMirrorSwitchListener?,
            onCornerRadiusChangeListener: OnCornerRadiusChangeListener?,
            onAlphaChangeListener: OnAlphaChangeListener?,
            onControlSwitchListener: OnControlSwitchListener?,
            onBorderSwitchListener: OnBorderSwitchListener?,
            onBorderColorChangeListener: OnBorderColorChangeListener?,
            onBorderWidthChangeListener: OnBorderWidthChangeListener?,
            onNoteChangeListener: OnNoteChangeListener?,
            onWindowDeleteListener: OnWindowDeleteListener?,
            onVideoControlListener: OnVideoControlListener?,
            onLandscapeSwitchListener: OnLandscapeSwitchListener?,
            onEditSwitchListener: OnEditSwitchListener?
        ) {
            // 只更新监听器设置，保持当前UI状态不变

            // 更新变换功能开关监听器
            if (!isRemoteMode) {
                switchDrag.setOnCheckedChangeListener { _, isChecked ->
                    onTransformSwitchListener?.onDragSwitchChanged(windowInfo.connectionId, isChecked)
                }
                switchScale.setOnCheckedChangeListener { _, isChecked ->
                    onTransformSwitchListener?.onScaleSwitchChanged(windowInfo.connectionId, isChecked)
                }
                switchRotation.setOnCheckedChangeListener { _, isChecked ->
                    onTransformSwitchListener?.onRotationSwitchChanged(windowInfo.connectionId, isChecked)
                }
            } else {
                switchDrag.setOnCheckedChangeListener(null)
                switchScale.setOnCheckedChangeListener(null)
                switchRotation.setOnCheckedChangeListener(null)
            }

            // 更新其他开关监听器
            if (!isRemoteMode) {
                switchCrop.setOnCheckedChangeListener { _, isChecked ->
                    onCropSwitchListener?.onCropSwitchChanged(windowInfo.connectionId, isChecked)
                }
                switchVisible.setOnCheckedChangeListener { _, isChecked ->
                    onVisibilitySwitchListener?.onVisibilitySwitchChanged(windowInfo.connectionId, isChecked)
                }
                switchMirror.setOnCheckedChangeListener { _, isChecked ->
                    onMirrorSwitchListener?.onMirrorSwitchChanged(windowInfo.connectionId, isChecked)
                }
                switchControl.setOnCheckedChangeListener { _, isChecked ->
                    onControlSwitchListener?.onControlSwitchChanged(windowInfo.connectionId, isChecked)
                }
                switchBorder.setOnCheckedChangeListener { _, isChecked ->
                    onBorderSwitchListener?.onBorderSwitchChanged(windowInfo.connectionId, isChecked)
                    updateColorPaletteState(isChecked)
                    updateBorderWidthState(isChecked)
                }

                // 📝 编辑开关监听器（仅文字窗口）
                if (windowInfo.connectionId.startsWith("text_")) {
                    switchEdit.setOnCheckedChangeListener { _, isChecked ->
                        AppLog.d("【编辑开关】监听器更新时用户操作: ${windowInfo.connectionId} -> $isChecked")
                        onEditSwitchListener?.onEditSwitchChanged(windowInfo.connectionId, isChecked)
                    }
                } else {
                    switchEdit.setOnCheckedChangeListener(null)
                }
            } else {
                switchCrop.setOnCheckedChangeListener(null)
                switchVisible.setOnCheckedChangeListener(null)
                switchMirror.setOnCheckedChangeListener(null)
                switchControl.setOnCheckedChangeListener(null)
                switchBorder.setOnCheckedChangeListener(null)
                switchEdit.setOnCheckedChangeListener(null)
            }

            AppLog.d("【监听器更新】已更新监听器设置，保持UI状态: ${windowInfo.connectionId}")
        }

        /**
         * 🎨 更新色板图标状态
         */
        private fun updateColorPaletteState(borderEnabled: Boolean) {
            // 🎯 修复：不设置isEnabled，只改变透明度和可点击性
            ivColorPalette.alpha = if (borderEnabled) 1.0f else 0.3f
            ivColorPalette.isClickable = borderEnabled
            AppLog.d("🎨 色板图标状态更新: enabled=$borderEnabled, alpha=${ivColorPalette.alpha}")
        }

        /**
         * 🎯 更新边框宽度滑动条状态
         */
        private fun updateBorderWidthState(borderEnabled: Boolean) {
            seekbarBorderWidth.isEnabled = borderEnabled
            seekbarBorderWidth.alpha = if (borderEnabled) 1.0f else 0.3f
            AppLog.d("🎯 边框宽度滑动条状态更新: enabled=$borderEnabled, alpha=${seekbarBorderWidth.alpha}")
        }

        /**
         * 显示颜色选择对话框
         */
        private fun showColorPickerDialog(connectionId: String, currentBorderColor: Int, listener: OnBorderColorChangeListener?) {
            try {
                AppLog.d("🎨 开始创建颜色选择对话框: $connectionId, 颜色: ${String.format("#%06X", 0xFFFFFF and currentBorderColor)}")
                val context = itemView.context
                val colorPickerDialog = com.example.castapp.ui.dialog.ColorPickerDialog(
                    context = context,
                    currentColor = currentBorderColor,
                    title = "🎨 边框颜色",
                    isStrokeMode = false,
                    currentStrokeWidth = 2.0f,
                    currentStrokeEnabled = false,
                    onColorSelected = { selectedColor ->
                        AppLog.d("🎨 用户选择了颜色: ${String.format("#%06X", 0xFFFFFF and selectedColor)}")
                        listener?.onBorderColorChanged(connectionId, selectedColor)
                    },
                    onStrokeConfigChanged = null
                )
                AppLog.d("🎨 颜色选择对话框创建成功，准备调用show()")
                colorPickerDialog.show()
                AppLog.d("🎨 颜色选择对话框show()方法已调用")
            } catch (e: Exception) {
                AppLog.e("🎨 创建或显示颜色选择对话框失败: $connectionId", e)
            }
        }

        /**
         * 🏷️ 显示备注编辑对话框
         */
        private fun showNoteEditDialog(windowInfo: CastWindowInfo, onNoteChangeListener: OnNoteChangeListener?) {
            try {
                val context = itemView.context
                val noteEditDialog = com.example.castapp.ui.dialog.NoteEditDialog(
                    context = context,
                    connectionId = windowInfo.connectionId,
                    deviceName = windowInfo.deviceName,
                    currentNote = windowInfo.note
                ) { newNote ->
                    // 备注更新回调
                    tvDeviceNote.text = if (newNote.isNotEmpty() && newNote != "无") {
                        newNote
                    } else {
                        "无"
                    }

                    // 🏷️ 通知监听器更新底层数据
                    onNoteChangeListener?.onNoteChanged(windowInfo.connectionId, newNote)

                    AppLog.d("🏷️ 备注已更新: ${windowInfo.connectionId} -> $newNote")
                }
                noteEditDialog.show()
                AppLog.d("🏷️ 备注编辑对话框已显示: ${windowInfo.connectionId}")
            } catch (e: Exception) {
                AppLog.e("🏷️ 显示备注编辑对话框失败: ${windowInfo.connectionId}", e)
            }
        }

        /**
         * 🗑️ 显示删除确认对话框 - 优化版本
         * 确保对话框在用户点击删除后立即消失，提升用户体验
         */
        private fun showDeleteConfirmDialog(windowInfo: CastWindowInfo, onWindowDeleteListener: OnWindowDeleteListener?) {
            try {
                val context = itemView.context

                // 根据窗口类型确定删除提示文本
                val windowType = when (windowInfo.connectionId) {
                    "front_camera" -> "前置摄像头窗口"
                    "rear_camera" -> "后置摄像头窗口"
                    else -> "投屏窗口"
                }

                val deviceName = if (!windowInfo.deviceName.isNullOrBlank()) {
                    windowInfo.deviceName
                } else {
                    "未知设备"
                }

                val dialog = AlertDialog.Builder(context)
                    .setTitle("确认删除")
                    .setMessage("确定要删除${windowType}「${deviceName}」吗？此操作不可撤销。")
                    .setPositiveButton("删除") { dialog, _ ->
                        // 🚀 优化：立即关闭对话框，提升用户体验
                        dialog.dismiss()
                        AppLog.d("🗑️ 删除确认对话框已立即关闭: ${windowInfo.connectionId}")

                        // 🚀 异步执行删除操作，避免阻塞UI
                        android.os.Handler(android.os.Looper.getMainLooper()).post {
                            try {
                                onWindowDeleteListener?.onWindowDelete(windowInfo)
                                AppLog.d("🗑️ 用户确认删除窗口: ${windowInfo.connectionId}")
                            } catch (e: Exception) {
                                AppLog.e("🗑️ 执行删除操作失败: ${windowInfo.connectionId}", e)
                            }
                        }
                    }
                    .setNegativeButton("取消", null)
                    .create()

                dialog.show()
                AppLog.d("🗑️ 删除确认对话框已显示: ${windowInfo.connectionId}")
            } catch (e: Exception) {
                AppLog.e("🗑️ 显示删除确认对话框失败: ${windowInfo.connectionId}", e)
            }
        }

        /**
         * 🎬 设置视频控制功能
         */
        private fun setupVideoControls(windowInfo: CastWindowInfo, isRemoteMode: Boolean, onVideoControlListener: OnVideoControlListener?) {
            if (isRemoteMode || onVideoControlListener == null) return

            val context = itemView.context
            val connectionId = windowInfo.connectionId

            // 从SharedPreferences加载视频设置
            val prefs = context.getSharedPreferences("video_settings", android.content.Context.MODE_PRIVATE)
            val isPlayEnabled = prefs.getBoolean("${connectionId}_play_enabled", true)
            val loopCount = prefs.getInt("${connectionId}_loop_count", -1)
            val volume = prefs.getInt("${connectionId}_volume", 80)

            // 设置播放开关
            switchVideoPlay.isChecked = isPlayEnabled
            switchVideoPlay.setOnCheckedChangeListener { _, isChecked ->
                prefs.edit {
                    putBoolean("${connectionId}_play_enabled", isChecked)
                }
                onVideoControlListener.onVideoPlaySwitchChanged(connectionId, isChecked)
                AppLog.d("🎬 视频播放开关: $connectionId = $isChecked")
            }

            // 设置播放次数按钮
            btnVideoLoopCount.text = if (loopCount == -1) "∞" else loopCount.toString()
            btnVideoLoopCount.setOnClickListener {
                showLoopCountDialog(context, connectionId, loopCount, prefs, onVideoControlListener)
            }

            // 设置音量滑动条
            seekbarVideoVolume.progress = volume
            tvVideoVolumeValue.text = "${volume}%"
            seekbarVideoVolume.setOnSeekBarChangeListener(object : SeekBar.OnSeekBarChangeListener {
                override fun onProgressChanged(seekBar: SeekBar?, progress: Int, fromUser: Boolean) {
                    if (fromUser) {
                        tvVideoVolumeValue.text = "${progress}%"
                    }
                }
                override fun onStartTrackingTouch(seekBar: SeekBar?) {}
                override fun onStopTrackingTouch(seekBar: SeekBar?) {
                    val newVolume = seekBar?.progress ?: 80
                    prefs.edit {
                        putInt("${connectionId}_volume", newVolume)
                    }
                    onVideoControlListener.onVideoVolumeChanged(connectionId, newVolume)
                    AppLog.d("🎬 视频音量调整: $connectionId = ${newVolume}%")
                }
            })
        }

        /**
         * 🎬 显示播放次数设置对话框
         */
        private fun showLoopCountDialog(
            context: android.content.Context,
            connectionId: String,
            currentLoopCount: Int,
            prefs: android.content.SharedPreferences,
            onVideoControlListener: OnVideoControlListener
        ) {
            val editText = android.widget.EditText(context).apply {
                hint = "输入播放次数，留空表示无限循环"
                if (currentLoopCount != -1) {
                    setText(currentLoopCount.toString())
                }
                inputType = android.text.InputType.TYPE_CLASS_NUMBER
            }

            AlertDialog.Builder(context)
                .setTitle("设置播放次数")
                .setMessage("请输入播放次数（留空表示无限循环）：")
                .setView(editText)
                .setPositiveButton("确定") { _, _ ->
                    val input = editText.text.toString().trim()
                    val newLoopCount = if (input.isEmpty()) {
                        -1 // 无限循环
                    } else {
                        try {
                            val count = input.toInt()
                            if (count < 0) -1 else count
                        } catch (_: NumberFormatException) {
                            -1
                        }
                    }

                    // 保存设置
                    prefs.edit {
                        putInt("${connectionId}_loop_count", newLoopCount)
                    }

                    // 更新按钮显示
                    btnVideoLoopCount.text = if (newLoopCount == -1) "∞" else newLoopCount.toString()

                    // 通知监听器
                    onVideoControlListener.onVideoLoopCountChanged(connectionId, newLoopCount)
                    AppLog.d("🎬 视频播放次数设置: $connectionId = $newLoopCount")
                }
                .setNegativeButton("取消", null)
                .show()
        }
    }

    /**
     * DiffUtil回调，用于高效更新列表
     */
    private class WindowDiffCallback : DiffUtil.ItemCallback<CastWindowInfo>() {
        override fun areItemsTheSame(oldItem: CastWindowInfo, newItem: CastWindowInfo): Boolean {
            return oldItem.connectionId == newItem.connectionId
        }

        override fun areContentsTheSame(oldItem: CastWindowInfo, newItem: CastWindowInfo): Boolean {
            return oldItem == newItem
        }
    }


}
